﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net9.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="textPages.json" />
    <None Remove="background.png" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="textPages.json" />
    <Content Include="../background.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="itext" Version="9.2.0" />
    <PackageReference Include="Pdfium.NET" Version="1.0.12" />
  </ItemGroup>

</Project>
