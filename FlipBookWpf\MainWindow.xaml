﻿<Window x:Class="PDFInteractiveApp.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Interactive PDF Viewer - Document de Formation" Height="700" Width="1200"
        WindowStartupLocation="CenterScreen"
        KeyDown="Window_KeyDown">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="300"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Grid.ColumnSpan="2" Background="#2C3E50" Padding="10">
            <StackPanel Orientation="Horizontal">
                <Button Name="LoadPDF" Content="📄 Charger PDF" Click="LoadPDF_Click"
                        Padding="12,8" Margin="0,0,15,0" Background="#3498DB"
                        Foreground="White" BorderBrush="Transparent"
                        FontWeight="Medium">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                BorderThickness="{TemplateBinding BorderThickness}"
                                                CornerRadius="3">
                                            <ContentPresenter HorizontalAlignment="Center"
                                                            VerticalAlignment="Center"
                                                            Margin="{TemplateBinding Padding}"/>
                                        </Border>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>

                <TextBox Name="SearchBox" Width="250" Height="32" Margin="10,0"
                         KeyUp="SearchBox_KeyUp" VerticalContentAlignment="Center"
                         Text="Rechercher dans le document..." FontSize="13"
                         Foreground="#7F8C8D" Padding="8"/>

                <Button Name="SearchButton" Content="🔍 Rechercher" Click="SearchButton_Click"
                        Padding="12,8" Background="#27AE60" Foreground="White"
                        BorderBrush="Transparent" FontWeight="Medium">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                BorderThickness="{TemplateBinding BorderThickness}"
                                                CornerRadius="3">
                                            <ContentPresenter HorizontalAlignment="Center"
                                                            VerticalAlignment="Center"
                                                            Margin="{TemplateBinding Padding}"/>
                                        </Border>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>
            </StackPanel>
        </Border>

        <!-- Table of Contents -->
        <Border Grid.Row="1" Grid.Column="0" BorderBrush="#BDC3C7" BorderThickness="0,0,1,0">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <TextBlock Text="Table des matières" FontWeight="Bold" FontSize="16"
                           Padding="15,12" Background="#34495E" Foreground="White"/>

                <ListBox Name="TocList" Grid.Row="1" BorderThickness="0"
                         SelectionChanged="TocList_SelectionChanged"
                         ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                         Background="#F8F9FA">
                    <ListBox.ItemTemplate>
                        <DataTemplate>
                            <Border Padding="5,3" Margin="0,1">
                                <Border.Style>
                                    <Style TargetType="Border">
                                        <Setter Property="Background" Value="Transparent"/>
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding IsChapter}" Value="True">
                                                <Setter Property="Background" Value="#E8F4FD"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Border.Style>

                                <StackPanel Orientation="Horizontal">
                                    <TextBlock TextWrapping="Wrap" MaxWidth="240">
                                        <TextBlock.Style>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="Text" Value="{Binding Title}"/>
                                                <Setter Property="FontSize" Value="12"/>
                                                <Setter Property="Foreground" Value="#2C3E50"/>
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding IsChapter}" Value="True">
                                                        <Setter Property="FontWeight" Value="Bold"/>
                                                        <Setter Property="FontSize" Value="13"/>
                                                        <Setter Property="Foreground" Value="#1ABC9C"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    </TextBlock>

                                    <TextBlock Margin="8,0,0,0" VerticalAlignment="Bottom">
                                        <TextBlock.Style>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="Text" Value="{Binding PageNumber}"/>
                                                <Setter Property="FontSize" Value="11"/>
                                                <Setter Property="Foreground" Value="#7F8C8D"/>
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding IsChapter}" Value="True">
                                                        <Setter Property="FontWeight" Value="Bold"/>
                                                        <Setter Property="Foreground" Value="#16A085"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    </TextBlock>
                                </StackPanel>
                            </Border>
                        </DataTemplate>
                    </ListBox.ItemTemplate>
                </ListBox>
            </Grid>
        </Border>

        <!-- Main Content Area -->
        <Border Grid.Row="1" Grid.Column="1" Padding="25" Background="#FFFFFF">
            <ScrollViewer VerticalScrollBarVisibility="Auto"
                          HorizontalScrollBarVisibility="Disabled">
                <StackPanel>
                    <TextBlock Name="PageTitle" FontSize="26" FontWeight="Bold"
                               Foreground="#2C3E50" Margin="0,0,0,25"
                               TextWrapping="Wrap"/>

                    <TextBox Name="PageContent" FontSize="14" TextWrapping="Wrap"
                             Foreground="#34495E"
                             FontFamily="Segoe UI, Arial, sans-serif"
                             IsReadOnly="True"
                             BorderThickness="0"
                             Background="Transparent"
                             IsTabStop="False"
                             Cursor="IBeam"/>
                </StackPanel>
            </ScrollViewer>
        </Border>

        <!-- Navigation Footer -->
        <Border Grid.Row="2" Grid.ColumnSpan="2" Background="#ECF0F1" Padding="15,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <Button Name="PrevButton" Content="← Page précédente" Click="PrevButton_Click"
                        Padding="15,8" Background="#95A5A6" Foreground="White"
                        BorderBrush="Transparent" FontWeight="Medium">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                BorderThickness="{TemplateBinding BorderThickness}"
                                                CornerRadius="3">
                                            <ContentPresenter HorizontalAlignment="Center"
                                                            VerticalAlignment="Center"
                                                            Margin="{TemplateBinding Padding}"/>
                                        </Border>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>

                <TextBlock Name="PageNumber" Grid.Column="1" Text="Page 1 of 1"
                           HorizontalAlignment="Center" VerticalAlignment="Center"
                           FontWeight="Medium" FontSize="14" Foreground="#2C3E50"/>

                <Button Name="NextButton" Grid.Column="2" Content="Page suivante →"
                        Click="NextButton_Click" Padding="15,8" Background="#95A5A6"
                        Foreground="White" BorderBrush="Transparent"
                        FontWeight="Medium">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                BorderThickness="{TemplateBinding BorderThickness}"
                                                CornerRadius="3">
                                            <ContentPresenter HorizontalAlignment="Center"
                                                            VerticalAlignment="Center"
                                                            Margin="{TemplateBinding Padding}"/>
                                        </Border>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>
            </Grid>
        </Border>
    </Grid>
</Window>