{"Version": 1, "WorkspaceRootPath": "D:\\Projects\\FlipBookWpf\\FlipBookWpf\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{69BAA6D1-20B0-4390-8D20-B37CC887B069}|FlipBookWpf\\FlipBookWpf.csproj|d:\\projects\\flipbookwpf\\flipbookwpf\\flipbookwpf\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{69BAA6D1-20B0-4390-8D20-B37CC887B069}|FlipBookWpf\\FlipBookWpf.csproj|solutionrelative:flipbookwpf\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{69BAA6D1-20B0-4390-8D20-B37CC887B069}|FlipBookWpf\\FlipBookWpf.csproj|d:\\projects\\flipbookwpf\\flipbookwpf\\flipbookwpf\\mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{69BAA6D1-20B0-4390-8D20-B37CC887B069}|FlipBookWpf\\FlipBookWpf.csproj|solutionrelative:flipbookwpf\\mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "MainWindow.xaml", "DocumentMoniker": "D:\\Projects\\FlipBookWpf\\FlipBookWpf\\FlipBookWpf\\MainWindow.xaml", "RelativeDocumentMoniker": "FlipBookWpf\\MainWindow.xaml", "ToolTip": "D:\\Projects\\FlipBookWpf\\FlipBookWpf\\FlipBookWpf\\MainWindow.xaml", "RelativeToolTip": "FlipBookWpf\\MainWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-07-15T01:41:17.813Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "MainWindow.xaml.cs", "DocumentMoniker": "D:\\Projects\\FlipBookWpf\\FlipBookWpf\\FlipBookWpf\\MainWindow.xaml.cs", "RelativeDocumentMoniker": "FlipBookWpf\\MainWindow.xaml.cs", "ToolTip": "D:\\Projects\\FlipBookWpf\\FlipBookWpf\\FlipBookWpf\\MainWindow.xaml.cs", "RelativeToolTip": "FlipBookWpf\\MainWindow.xaml.cs", "ViewState": "AgIAANMBAAAAAAAAAAAwwOABAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-15T01:41:08.038Z", "EditorCaption": ""}]}]}]}