﻿﻿using System.Windows;

namespace PDFInteractiveApp
{
    /// <summary>
    /// Interaction logic for CoverWindow.xaml
    /// </summary>
    public partial class CoverWindow : Window
    {
        public CoverWindow()
        {
            InitializeComponent();
        }

        private void StartReadingButton_Click(object sender, RoutedEventArgs e)
        {
            // Create and show the main window
            MainWindow mainWindow = new MainWindow();
            mainWindow.Show();
            
            // Close the cover window
            this.Close();
        }
    }
}
