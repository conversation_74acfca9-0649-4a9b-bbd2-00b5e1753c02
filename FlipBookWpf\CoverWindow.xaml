﻿<Window x:Class="PDFInteractiveApp.CoverWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Document de Formation"
        Height="700" Width="1200"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        ResizeMode="CanResize">

    <Grid>
        <!-- Background Image -->
        <Image Source="background.png"
               Stretch="UniformToFill"
               HorizontalAlignment="Center"
               VerticalAlignment="Center"/>

        <!-- Overlay for better button visibility -->
        <Rectangle Fill="Black" Opacity="0.3" />

        <!-- Content Overlay -->
        <Grid VerticalAlignment="Center" HorizontalAlignment="Center">
            <StackPanel Orientation="Vertical" HorizontalAlignment="Center" VerticalAlignment="Center">

                <!-- Welcome Text -->
                <TextBlock Text="Document de Formation"
                           FontSize="48"
                           FontWeight="Bold"
                           Foreground="White"
                           HorizontalAlignment="Center"
                           Margin="0,0,0,60"
                           Effect="{DynamicResource DropShadowEffect}"/>

                <!-- Start Reading Button -->
                <Button Name="StartReadingButton"
                        Content="📖 Commencer la lecture"
                        Click="StartReadingButton_Click"
                        FontSize="18"
                        FontWeight="Bold"
                        Padding="30,15"
                        Background="#3498DB"
                        Foreground="White"
                        BorderBrush="Transparent"
                        Cursor="Hand">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                BorderThickness="{TemplateBinding BorderThickness}"
                                                CornerRadius="8">
                                            <ContentPresenter HorizontalAlignment="Center"
                                                            VerticalAlignment="Center"
                                                            Margin="{TemplateBinding Padding}"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#2980B9"/>
                                            </Trigger>
                                            <Trigger Property="IsPressed" Value="True">
                                                <Setter Property="Background" Value="#21618C"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>

            </StackPanel>
        </Grid>
    </Grid>

    <Window.Resources>
        <DropShadowEffect x:Key="DropShadowEffect"
                          Color="Black"
                          BlurRadius="5"
                          ShadowDepth="2"
                          Opacity="0.8"/>
    </Window.Resources>
</Window>
