﻿using iText.Kernel.Pdf;
using iText.Kernel.Pdf.Canvas.Parser;
using Microsoft.Win32;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media.Animation;
using System.Linq;

namespace PDFInteractiveApp
{
    public partial class MainWindow : Window
    {
        private ObservableCollection<PageContent> pages;
        private ObservableCollection<TocItem> tableOfContents;
        private int currentPageIndex = 0;

        public MainWindow()
        {
            InitializeComponent();
            pages = new ObservableCollection<PageContent>();
            tableOfContents = new ObservableCollection<TocItem>();

            TocList.ItemsSource = tableOfContents;

            // Initialize with sample data
            InitializeSampleData();

            if (pages.Count > 0)
            {
                DisplayPage(0);
            }
        }

        private void InitializeSampleData()
        {
            // Initialize table of contents first
            InitializePredefinedTableOfContents();

            // Create sample pages for all TOC entries
            CreateSamplePagesFromTOC();
        }

        private void InitializePredefinedTableOfContents()
        {
            // Clear existing TOC
            tableOfContents.Clear();

            // Chapitre 1: Développement du personnel
            var chapter1Items = new[]
            {
                new { Title = "La communication professionnelle", Page = 1 },
                new { Title = "Concevoir et déployer le plan de communication", Page = 3 },
                new { Title = "Connaître son environnement réglementaire RH", Page = 5 },
                new { Title = "Développer son leadership", Page = 7 },
                new { Title = "Entretien d'évaluation et de développement", Page = 9 },
                new { Title = "Finance pour non-financiers", Page = 11 },
                new { Title = "Gestion de stress", Page = 13 },
                new { Title = "Gestion du temps et des priorités", Page = 15 },
                new { Title = "Insure Your Skills", Page = 17 },
                new { Title = "La communication interpersonnelle", Page = 19 },
                new { Title = "Mener efficacement un entretien d'embauche", Page = 21 },
                new { Title = "Prise de parole en public", Page = 23 },
                new { Title = "Techniques de résolution de problèmes", Page = 25 }
            };

            // Chapitre 2: Vente, Accueil Client & Marketing
            var chapter2Items = new[]
            {
                new { Title = "Animation d'un réseau de vente", Page = 27 },
                new { Title = "Conquête et fidélisation des clients", Page = 29 },
                new { Title = "Définir et déployer son plan marketing", Page = 31 },
                new { Title = "Développer et gérer son portefeuille client", Page = 33 },
                new { Title = "L'essentiel du marketing", Page = 35 },
                new { Title = "Gérer et négocier avec les grands comptes", Page = 37 },
                new { Title = "Gestion des clients difficiles", Page = 39 },
                new { Title = "Les clés du marketing digital", Page = 41 },
                new { Title = "Le marketing opérationnel et le plan marketing omnicanal", Page = 43 },
                new { Title = "Techniques de négociation", Page = 45 }
            };

            // Chapitre 3: Conformité, Juridique, Contrôle & Audit
            var chapter3Items = new[]
            {
                new { Title = "Contrôle de gestion : maîtrise des coûts de revient", Page = 47 },
                new { Title = "Contrôle interne et management des risques COSO I & II", Page = 49 },
                new { Title = "Diagnostic et optimisation d'un contrat d'assurance", Page = 51 },
                new { Title = "La cartographie des risques au service d'un audit interne", Page = 53 },
                new { Title = "La fonction conformité", Page = 55 },
                new { Title = "Le contrôle de gestion au service de la performance", Page = 57 },
                new { Title = "Le risque juridique en entreprise", Page = 59 }
            };

            // Chapitre 4: Métiers des Assurances
            var chapter4Items = new[]
            {
                new { Title = "Actuariat non-vie", Page = 61 },
                new { Title = "Assurance automobile", Page = 63 },
                new { Title = "Assurance risques techniques", Page = 65 },
                new { Title = "Assurance risques simples", Page = 67 },
                new { Title = "Droit du contrat d'assurance", Page = 69 },
                new { Title = "Gestion des sinistres", Page = 71 },
                new { Title = "La fraude à l'assurance", Page = 73 },
                new { Title = "Gestion des risques agricoles", Page = 75 },
                new { Title = "L'assurance responsabilité civile", Page = 77 },
                new { Title = "L'assurance transport", Page = 79 },
                new { Title = "Les bases techniques et juridiques des contrats d'assurance", Page = 81 },
                new { Title = "Les bases du règlement des sinistres pertes d'exploitation", Page = 83 },
                new { Title = "Marché des assurances", Page = 85 },
                new { Title = "Pratique de l'actuariat", Page = 87 },
                new { Title = "Prévention, assurance et réassurance incendie", Page = 89 },
                new { Title = "Takaful & Tontines", Page = 91 }
            };

            // Chapitre 5: Ressources Humaines
            var chapter5Items = new[]
            {
                new { Title = "Administration du personnel", Page = 93 },
                new { Title = "Audit RH", Page = 95 },
                new { Title = "Gestion de la paie", Page = 97 },
                new { Title = "Politique et stratégie de la rémunération", Page = 99 },
                new { Title = "Gestion des moyens généraux", Page = 101 },
                new { Title = "Gestion du parc roulant", Page = 103 },
                new { Title = "Législation du travail et rédaction des contrats", Page = 105 },
                new { Title = "Les fondamentaux de la GRH", Page = 107 },
                new { Title = "Magasinage et entreposage", Page = 109 },
                new { Title = "Marketing RH : valorisation de l'image employeur", Page = 111 },
                new { Title = "Tableau de bord RH", Page = 113 }
            };

            // Chapitre 6: Finances et Comptabilité
            var chapter6Items = new[]
            {
                new { Title = "Contentieux fiscal", Page = 115 },
                new { Title = "Correction d'erreurs comptables", Page = 117 },
                new { Title = "Gestion de la trésorerie", Page = 119 },
                new { Title = "Impôts différés et exigibles", Page = 121 },
                new { Title = "L'analyse financière", Page = 123 },
                new { Title = "Levée de réserves commissaire aux comptes", Page = 125 },
                new { Title = "Loi de finance", Page = 127 },
                new { Title = "Passer à la comptabilité analytique", Page = 129 },
                new { Title = "Tableau des flux de trésorerie", Page = 131 },
                new { Title = "Techniques d'analyse financière", Page = 133 },
                new { Title = "Travaux de fin d'année", Page = 135 }
            };

            // Chapitre 7: IT
            var chapter7Items = new[]
            {
                new { Title = "Administration Oracle Database", Page = 137 },
                new { Title = "Administration Docker", Page = 139 },
                new { Title = "Administration WAF FortiWeb", Page = 141 },
                new { Title = "Administration Kaspersky", Page = 143 },
                new { Title = "Management de projet PMBOK", Page = 145 },
                new { Title = "Administration Oracle Linux 8", Page = 147 },
                new { Title = "Maîtrise Oracle SQL", Page = 149 }
            };

            // Add all chapters to table of contents
            AddChapterToTOC("Chapitre 1", "Développement du personnel", chapter1Items);
            AddChapterToTOC("Chapitre 2", "Vente, Accueil Client & Marketing", chapter2Items);
            AddChapterToTOC("Chapitre 3", "Conformité, Juridique, Contrôle & Audit", chapter3Items);
            AddChapterToTOC("Chapitre 4", "Métiers des Assurances", chapter4Items);
            AddChapterToTOC("Chapitre 5", "Ressources Humaines", chapter5Items);
            AddChapterToTOC("Chapitre 6", "Finances et Comptabilité", chapter6Items);
            AddChapterToTOC("Chapitre 7", "IT", chapter7Items);
        }

        private void CreateSamplePagesFromTOC()
        {
            // Create sample pages for all TOC entries
            foreach (var tocItem in tableOfContents)
            {
                // Skip if page already exists
                if (pages.Any(p => p.PageNumber == tocItem.PageNumber))
                    continue;

                // Create sample content based on the TOC item
                string sampleContent = GenerateSampleContent(tocItem);

                pages.Add(new PageContent
                {
                    PageNumber = tocItem.PageNumber,
                    Title = tocItem.Title.Trim(),
                    Content = sampleContent,
                    IsChapter = tocItem.IsChapter,
                    Chapter = tocItem.Chapter
                });
            }

            // Sort pages by page number
            var sortedPages = pages.OrderBy(p => p.PageNumber).ToList();
            pages.Clear();
            foreach (var page in sortedPages)
            {
                pages.Add(page);
            }
        }

        private string GenerateSampleContent(TocItem tocItem)
        {
            if (tocItem.IsChapter)
            {
                return $"{tocItem.Title}\n\nCeci est le contenu d'exemple pour {tocItem.Title}.\n\nCe chapitre couvre les aspects essentiels de ce sujet, incluant les concepts fondamentaux, les meilleures pratiques, et les applications pratiques.\n\nContenu détaillé à venir...";
            }
            else
            {
                return $"{tocItem.Title.Trim()}\n\nCeci est le contenu d'exemple pour la section: {tocItem.Title.Trim()}\n\nCette section fait partie du chapitre {tocItem.Chapter}.\n\nPoints clés:\n• Concept principal\n• Applications pratiques\n• Exemples concrets\n• Exercices et cas d'étude\n\nPour plus d'informations détaillées, veuillez consulter la documentation complète.";
            }
        }

        private void AddChapterToTOC(string chapterNumber, string chapterTitle, dynamic[] items)
        {
            // Add chapter header
            tableOfContents.Add(new TocItem
            {
                Title = $"{chapterNumber} - {chapterTitle}",
                PageNumber = items[0].Page,
                IsChapter = true,
                Chapter = chapterTitle
            });

            // Add chapter items
            foreach (var item in items)
            {
                tableOfContents.Add(new TocItem
                {
                    Title = $"    {item.Title}",
                    PageNumber = item.Page,
                    IsChapter = false,
                    Chapter = chapterTitle
                });
            }
        }

        private void LoadPDF_Click(object sender, RoutedEventArgs e)
        {
            OpenFileDialog openFileDialog = new OpenFileDialog
            {
                Filter = "PDF files (*.pdf)|*.pdf|All files (*.*)|*.*",
                Title = "Select PDF Document"
            };

            if (openFileDialog.ShowDialog() == true)
            {
                try
                {
                    // Clear existing content but keep predefined TOC
                    pages.Clear();

                    // Load and process the PDF with predefined structure
                    LoadPdfDocumentWithPredefinedStructure(openFileDialog.FileName);

                    // Display first page
                    if (pages.Count > 0)
                    {
                        DisplayPage(0);
                    }

                    MessageBox.Show($"PDF loaded successfully!\nTotal pages: {pages.Count}",
                        "Success", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error loading PDF: {ex.Message}",
                        "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void LoadPdfDocumentWithPredefinedStructure(string pdfPath)
        {
            // Create PDF document reader
            using (PdfDocument pdfDoc = new PdfDocument(new PdfReader(pdfPath)))
            {
                int totalPages = pdfDoc.GetNumberOfPages();

                // Process each page using predefined structure
                for (int pageNum = 1; pageNum <= totalPages; pageNum++)
                {
                    try
                    {
                        PdfPage page = pdfDoc.GetPage(pageNum);
                        string text = PdfTextExtractor.GetTextFromPage(page);

                        // Find matching TOC entry for this page
                        var tocEntry = tableOfContents.FirstOrDefault(t => t.PageNumber == pageNum && !t.IsChapter);
                        var chapterEntry = tableOfContents.Where(t => t.PageNumber <= pageNum && t.IsChapter)
                                                       .OrderByDescending(t => t.PageNumber)
                                                       .FirstOrDefault();

                        // Create page content
                        var pageContent = new PageContent
                        {
                            PageNumber = pageNum,
                            Title = tocEntry?.Title?.Trim() ?? $"Page {pageNum}",
                            Content = CleanupText(text),
                            IsChapter = tocEntry?.IsChapter ?? false,
                            Chapter = chapterEntry?.Chapter ?? "Unknown"
                        };

                        pages.Add(pageContent);
                    }
                    catch (Exception ex)
                    {
                        // Handle individual page errors
                        Console.WriteLine($"Error processing page {pageNum}: {ex.Message}");

                        // Add placeholder page
                        pages.Add(new PageContent
                        {
                            PageNumber = pageNum,
                            Title = $"Page {pageNum}",
                            Content = "Error loading page content.",
                            IsChapter = false,
                            Chapter = "Unknown"
                        });
                    }
                }
            }
        }

        private string CleanupText(string text)
        {
            if (string.IsNullOrWhiteSpace(text))
                return "No content available.";

            // Remove excessive whitespace and normalize line breaks
            text = System.Text.RegularExpressions.Regex.Replace(text, @"\s+", " ");
            text = text.Replace("\r\n", "\n").Replace("\r", "\n");

            // Split into lines and process
            var lines = text.Split('\n', StringSplitOptions.RemoveEmptyEntries)
                           .Select(line => line.Trim())
                           .Where(line => !string.IsNullOrEmpty(line))
                           .ToList();

            // Remove duplicate consecutive lines
            var cleanedLines = new List<string>();
            string previousLine = "";

            foreach (var line in lines)
            {
                if (line != previousLine)
                {
                    cleanedLines.Add(line);
                    previousLine = line;
                }
            }

            // Join lines with proper spacing for readability
            var result = string.Join("\n\n", cleanedLines);

            // Limit content length for better performance
            if (result.Length > 5000)
            {
                result = result.Substring(0, 4997) + "...";
            }

            return result;
        }

        private void DisplayPage(int pageIndex)
        {
            if (pageIndex < 0 || pageIndex >= pages.Count) return;

            currentPageIndex = pageIndex;
            var page = pages[pageIndex];

            // Create page flip animation
            var fadeOut = new DoubleAnimation(1, 0, TimeSpan.FromMilliseconds(150));
            var fadeIn = new DoubleAnimation(0, 1, TimeSpan.FromMilliseconds(150));

            fadeOut.Completed += (s, e) =>
            {
                PageTitle.Text = page.Title;
                PageContent.Text = page.Content;
                PageNumber.Text = $"Page {page.PageNumber} of {pages.Count}";

                PageContent.BeginAnimation(UIElement.OpacityProperty, fadeIn);
            };

            PageContent.BeginAnimation(UIElement.OpacityProperty, fadeOut);

            // Update navigation buttons
            PrevButton.IsEnabled = pageIndex > 0;
            NextButton.IsEnabled = pageIndex < pages.Count - 1;

            // Highlight current page in TOC
            foreach (TocItem item in tableOfContents)
            {
                item.IsSelected = item.PageNumber == page.PageNumber;
            }
        }

        private void PrevButton_Click(object sender, RoutedEventArgs e)
        {
            if (currentPageIndex > 0)
            {
                DisplayPage(currentPageIndex - 1);
            }
        }

        private void NextButton_Click(object sender, RoutedEventArgs e)
        {
            if (currentPageIndex < pages.Count - 1)
            {
                DisplayPage(currentPageIndex + 1);
            }
        }

        private void TocList_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (TocList.SelectedItem is TocItem selectedItem)
            {
                var pageIndex = pages.ToList().FindIndex(p => p.PageNumber == selectedItem.PageNumber);
                if (pageIndex >= 0)
                {
                    DisplayPage(pageIndex);
                }
            }
        }

        private void SearchBox_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                SearchButton_Click(sender, e);
            }
        }

        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            string searchTerm = SearchBox.Text.ToLower();
            if (string.IsNullOrWhiteSpace(searchTerm)) return;

            for (int i = currentPageIndex + 1; i < pages.Count; i++)
            {
                if (pages[i].Content.ToLower().Contains(searchTerm) ||
                    pages[i].Title.ToLower().Contains(searchTerm))
                {
                    DisplayPage(i);
                    return;
                }
            }

            // Search from beginning if not found after current page
            for (int i = 0; i <= currentPageIndex; i++)
            {
                if (pages[i].Content.ToLower().Contains(searchTerm) ||
                    pages[i].Title.ToLower().Contains(searchTerm))
                {
                    DisplayPage(i);
                    return;
                }
            }

            MessageBox.Show("Search term not found.", "Search", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void Window_KeyDown(object sender, KeyEventArgs e)
        {
            switch (e.Key)
            {
                case Key.Left:
                case Key.PageUp:
                    PrevButton_Click(sender, e);
                    break;
                case Key.Right:
                case Key.PageDown:
                    NextButton_Click(sender, e);
                    break;
                case Key.Home:
                    DisplayPage(0);
                    break;
                case Key.End:
                    DisplayPage(pages.Count - 1);
                    break;
            }
        }
    }

    public class TocEntry
    {
        public string Title { get; set; }
        public int PageNumber { get; set; }
        public string Chapter { get; set; }
    }

    public class PageContent
    {
        public int PageNumber { get; set; }
        public string Title { get; set; }
        public string Content { get; set; }
        public bool IsChapter { get; set; }
        public string Chapter { get; set; }
        public List<string> Images { get; set; } = new List<string>();
    }

    public class TocItem
    {
        public string Title { get; set; }
        public int PageNumber { get; set; }
        public bool IsSelected { get; set; }
        public bool IsChapter { get; set; }
        public string Chapter { get; set; }
    }
}